{"format": 1, "restore": {"H:\\c#\\c#工具\\CsvTomarkdown\\CsvToMarkdown.csproj": {}}, "projects": {"H:\\c#\\c#工具\\CsvTomarkdown\\CsvToMarkdown.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\c#\\c#工具\\CsvTomarkdown\\CsvToMarkdown.csproj", "projectName": "CsvToMarkdown", "projectPath": "H:\\c#\\c#工具\\CsvTomarkdown\\CsvToMarkdown.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "H:\\c#\\c#工具\\CsvTomarkdown\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["H:\\Visual\\Program Files (x86)\\Shared\\NuGetPackages", "D:\\CSharpDec\\ToolboxNuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Telerik UI for WPF.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://nuget.hyc.com/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"CsvHelper": {"target": "Package", "version": "[30.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}}}}