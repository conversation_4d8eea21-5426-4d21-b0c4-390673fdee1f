using System;
using System.IO;
using System.Threading.Tasks;

namespace CsvToMarkdown
{
    public class SimpleTest
    {
        public static async Task TestConversion()
        {
            try
            {
                Console.WriteLine("开始测试CSV转换...");
                
                var converter = new CsvToMarkdownConverter();
                var testFile = Path.Combine("test_data", "sample.csv");
                
                Console.WriteLine($"测试文件: {testFile}");
                
                if (!File.Exists(testFile))
                {
                    Console.WriteLine("测试文件不存在！");
                    return;
                }

                // 测试单个文件转换
                var success = await converter.ConvertFileAsync(testFile);
                
                if (success)
                {
                    Console.WriteLine("转换成功！");
                    
                    // 检查生成的markdown文件
                    var markdownFile = Path.Combine("test_data", "sample.md");
                    if (File.Exists(markdownFile))
                    {
                        Console.WriteLine($"生成的文件: {markdownFile}");
                        var content = await File.ReadAllTextAsync(markdownFile);
                        Console.WriteLine("文件内容:");
                        Console.WriteLine(content);
                    }
                    else
                    {
                        Console.WriteLine("Markdown文件未生成！");
                    }
                }
                else
                {
                    Console.WriteLine("转换失败！");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }
        }
    }
}
