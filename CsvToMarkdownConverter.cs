using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CsvToMarkdown
{
    public class CsvToMarkdownConverter
    {
        /// <summary>
        /// 进度更新委托
        /// </summary>
        /// <param name="current">当前处理的文件索引</param>
        /// <param name="total">总文件数</param>
        /// <param name="fileName">当前处理的文件名</param>
        public delegate void ProgressUpdateHandler(int current, int total, string fileName);

        /// <summary>
        /// 转换单个CSV文件为Markdown
        /// </summary>
        /// <param name="csvFilePath">CSV文件路径</param>
        /// <param name="outputPath">输出Markdown文件路径（可选，默认为同目录下的.md文件）</param>
        /// <returns>转换是否成功</returns>
        public async Task<bool> ConvertFileAsync(string csvFilePath, string? outputPath = null)
        {
            try
            {
                // 如果没有指定输出路径，则在同目录下创建.md文件
                if (string.IsNullOrEmpty(outputPath))
                {
                    var directory = Path.GetDirectoryName(csvFilePath) ?? string.Empty;
                    var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(csvFilePath);
                    outputPath = Path.Combine(directory, $"{fileNameWithoutExtension}.md");
                }

                // 读取CSV数据
                var csvData = await Task.Run(() => CsvReader.ReadCsvFile(csvFilePath));

                // 转换为Markdown
                var markdownContent = await Task.Run(() => MarkdownConverter.ConvertToMarkdown(csvData));

                // 添加文件头信息
                var finalContent = CreateMarkdownWithHeader(csvFilePath, markdownContent);

                // 写入Markdown文件
                await File.WriteAllTextAsync(outputPath, finalContent, Encoding.UTF8);

                return true;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"转换文件 {Path.GetFileName(csvFilePath)} 失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量转换文件夹中的所有CSV文件
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        /// <param name="progressCallback">进度回调函数</param>
        /// <returns>转换结果统计</returns>
        public async Task<ConversionResult> ConvertFolderAsync(string folderPath, ProgressUpdateHandler? progressCallback = null)
        {
            return await ConvertFolderAsync(folderPath, null, progressCallback);
        }

        /// <summary>
        /// 批量转换文件夹中的所有CSV文件到指定输出目录
        /// </summary>
        /// <param name="folderPath">CSV文件夹路径</param>
        /// <param name="outputPath">输出目录路径（null表示与CSV文件同目录）</param>
        /// <param name="progressCallback">进度回调函数</param>
        /// <returns>转换结果统计</returns>
        public async Task<ConversionResult> ConvertFolderAsync(string folderPath, string? outputPath, ProgressUpdateHandler? progressCallback = null)
        {
            var result = new ConversionResult();

            try
            {
                // 获取所有CSV文件
                var csvFiles = Directory.GetFiles(folderPath, "*.csv", SearchOption.TopDirectoryOnly)
                                       .OrderBy(f => f)
                                       .ToArray();

                if (csvFiles.Length == 0)
                {
                    throw new InvalidOperationException("指定文件夹中没有找到CSV文件");
                }

                result.TotalFiles = csvFiles.Length;

                // 逐个处理文件
                for (int i = 0; i < csvFiles.Length; i++)
                {
                    var csvFile = csvFiles[i];
                    var fileName = Path.GetFileName(csvFile);

                    try
                    {
                        // 更新进度
                        progressCallback?.Invoke(i + 1, csvFiles.Length, fileName);

                        // 确定输出文件路径
                        string? outputFilePath = null;
                        if (!string.IsNullOrEmpty(outputPath))
                        {
                            var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(csvFile);
                            outputFilePath = Path.Combine(outputPath, $"{fileNameWithoutExtension}.md");
                        }

                        // 转换文件
                        await ConvertFileAsync(csvFile, outputFilePath);

                        result.SuccessfulFiles.Add(fileName);
                    }
                    catch (Exception ex)
                    {
                        result.FailedFiles.Add(new FailedFile { FileName = fileName, Error = ex.Message });
                    }
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"批量转换失败: {ex.Message}", ex);
            }

            return result;
        }

        /// <summary>
        /// 创建带有文件头信息的Markdown内容
        /// </summary>
        /// <param name="originalFilePath">原始CSV文件路径</param>
        /// <param name="markdownContent">Markdown表格内容</param>
        /// <returns>完整的Markdown内容</returns>
        private string CreateMarkdownWithHeader(string originalFilePath, string markdownContent)
        {
            var fileName = Path.GetFileName(originalFilePath);
            var creationTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            var header = new StringBuilder();
            header.AppendLine($"# {Path.GetFileNameWithoutExtension(fileName)}");
            header.AppendLine();
            header.AppendLine($"**原始文件:** {fileName}  ");
            header.AppendLine($"**转换时间:** {creationTime}  ");
            header.AppendLine($"**转换工具:** CSV to Markdown Converter");
            header.AppendLine();
            header.AppendLine("---");
            header.AppendLine();

            return header.ToString() + markdownContent;
        }
    }

    /// <summary>
    /// 转换结果统计
    /// </summary>
    public class ConversionResult
    {
        public int TotalFiles { get; set; }
        public List<string> SuccessfulFiles { get; set; } = new List<string>();
        public List<FailedFile> FailedFiles { get; set; } = new List<FailedFile>();

        public int SuccessCount => SuccessfulFiles.Count;
        public int FailureCount => FailedFiles.Count;
        public bool HasFailures => FailedFiles.Count > 0;
    }

    /// <summary>
    /// 失败的文件信息
    /// </summary>
    public class FailedFile
    {
        public string FileName { get; set; } = string.Empty;
        public string Error { get; set; } = string.Empty;
    }
}
