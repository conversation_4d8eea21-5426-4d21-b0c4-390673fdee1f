using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Text;
using CsvHelper;
using CsvHelper.Configuration;

namespace CsvToMarkdown
{
    public class CsvReader
    {
        /// <summary>
        /// 读取CSV文件并返回数据表格
        /// </summary>
        /// <param name="filePath">CSV文件路径</param>
        /// <returns>包含表头和数据行的列表</returns>
        public static List<List<string>> ReadCsvFile(string filePath)
        {
            var result = new List<List<string>>();
            
            try
            {
                // 尝试检测文件编码
                var encoding = DetectFileEncoding(filePath);
                
                using var reader = new StringReader(File.ReadAllText(filePath, encoding));
                using var csv = new CsvHelper.CsvReader(reader, GetCsvConfiguration());
                
                // 读取第一行作为表头
                if (csv.Read())
                {
                    var headers = new List<string>();
                    for (int i = 0; csv.TryGetField<string>(i, out string? field); i++)
                    {
                        headers.Add(field ?? string.Empty);
                    }
                    result.Add(headers);
                }
                
                // 读取数据行
                while (csv.Read())
                {
                    var row = new List<string>();
                    for (int i = 0; i < result[0].Count; i++)
                    {
                        csv.TryGetField<string>(i, out string? field);
                        row.Add(field ?? string.Empty);
                    }
                    result.Add(row);
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"读取CSV文件失败: {ex.Message}", ex);
            }
            
            return result;
        }
        
        /// <summary>
        /// 获取CSV配置
        /// </summary>
        private static CsvConfiguration GetCsvConfiguration()
        {
            return new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = false,
                MissingFieldFound = null,
                BadDataFound = null,
                TrimOptions = TrimOptions.Trim
            };
        }
        
        /// <summary>
        /// 检测文件编码
        /// </summary>
        private static Encoding DetectFileEncoding(string filePath)
        {
            // 读取文件的前几个字节来检测BOM
            var bytes = new byte[4];
            using (var fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                fs.Read(bytes, 0, 4);
            }
            
            // 检测UTF-8 BOM
            if (bytes[0] == 0xEF && bytes[1] == 0xBB && bytes[2] == 0xBF)
                return Encoding.UTF8;
            
            // 检测UTF-16 LE BOM
            if (bytes[0] == 0xFF && bytes[1] == 0xFE)
                return Encoding.Unicode;
            
            // 检测UTF-16 BE BOM
            if (bytes[0] == 0xFE && bytes[1] == 0xFF)
                return Encoding.BigEndianUnicode;
            
            // 尝试UTF-8（无BOM）
            try
            {
                var content = File.ReadAllText(filePath, Encoding.UTF8);
                // 如果没有抛出异常，说明UTF-8编码是有效的
                return Encoding.UTF8;
            }
            catch
            {
                // 如果UTF-8失败，使用系统默认编码
                return Encoding.Default;
            }
        }
    }
}
