using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace CsvToMarkdown
{
    public partial class MainForm : Form
    {
        private string selectedFolderPath = string.Empty;
        private string outputFolderPath = string.Empty;

        public MainForm()
        {
            InitializeComponent();
            UpdateOutputControls();
        }

        private void btnSelectFolder_Click(object sender, EventArgs e)
        {
            using var folderDialog = new FolderBrowserDialog();
            folderDialog.Description = "选择包含CSV文件的文件夹";
            folderDialog.ShowNewFolderButton = false;

            if (folderDialog.ShowDialog() == DialogResult.OK)
            {
                selectedFolderPath = folderDialog.SelectedPath;
                txtFolderPath.Text = selectedFolderPath;

                // 检查文件夹中是否有CSV文件
                var csvFiles = Directory.GetFiles(selectedFolderPath, "*.csv", SearchOption.TopDirectoryOnly);
                lblFileCount.Text = $"找到 {csvFiles.Length} 个CSV文件";
                UpdateConvertButtonState();
            }
        }

        private async void btnConvert_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(selectedFolderPath))
            {
                MessageBox.Show("请先选择文件夹", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // 检查输出目录
            string actualOutputPath = GetActualOutputPath();
            if (string.IsNullOrEmpty(actualOutputPath))
            {
                MessageBox.Show("请选择输出目录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                btnConvert.Enabled = false;
                btnSelectFolder.Enabled = false;
                btnSelectOutput.Enabled = false;
                btnOpenOutput.Enabled = false;
                progressBar.Visible = true;
                lblStatus.Text = "正在转换...";

                var converter = new CsvToMarkdownConverter();
                var result = await converter.ConvertFolderAsync(selectedFolderPath, actualOutputPath, UpdateProgress);

                lblStatus.Text = "转换完成！";
                btnOpenOutput.Enabled = true;

                string message = $"转换完成！\n总文件数: {result.TotalFiles}\n成功: {result.SuccessCount}\n失败: {result.FailureCount}";
                if (result.HasFailures)
                {
                    message += "\n\n失败的文件:\n" + string.Join("\n", result.FailedFiles.Select(f => $"- {f.FileName}: {f.Error}"));
                }

                MessageBox.Show(message, "转换完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                lblStatus.Text = "转换失败";
                MessageBox.Show($"转换过程中发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnConvert.Enabled = true;
                btnSelectFolder.Enabled = true;
                btnSelectOutput.Enabled = true;
                progressBar.Visible = false;
            }
        }

        private void UpdateProgress(int current, int total, string fileName)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<int, int, string>(UpdateProgress), current, total, fileName);
                return;
            }

            progressBar.Maximum = total;
            progressBar.Value = current;
            lblStatus.Text = $"正在处理: {fileName} ({current}/{total})";
        }

        private void btnSelectOutput_Click(object sender, EventArgs e)
        {
            using var folderDialog = new FolderBrowserDialog();
            folderDialog.Description = "选择Markdown文件输出目录";
            folderDialog.ShowNewFolderButton = true;

            if (folderDialog.ShowDialog() == DialogResult.OK)
            {
                outputFolderPath = folderDialog.SelectedPath;
                txtOutputPath.Text = outputFolderPath;
                UpdateConvertButtonState();
            }
        }

        private void chkSameFolder_CheckedChanged(object sender, EventArgs e)
        {
            UpdateOutputControls();
        }

        private void btnOpenOutput_Click(object sender, EventArgs e)
        {
            string pathToOpen = GetActualOutputPath();
            if (!string.IsNullOrEmpty(pathToOpen) && Directory.Exists(pathToOpen))
            {
                System.Diagnostics.Process.Start("explorer.exe", pathToOpen);
            }
            else
            {
                MessageBox.Show("输出目录不存在", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void UpdateOutputControls()
        {
            bool useSameFolder = chkSameFolder.Checked;
            lblOutputPath.Enabled = !useSameFolder;
            txtOutputPath.Enabled = !useSameFolder;
            btnSelectOutput.Enabled = !useSameFolder;

            if (useSameFolder)
            {
                txtOutputPath.Text = "与CSV文件相同目录";
                outputFolderPath = string.Empty;
            }
            else
            {
                txtOutputPath.Text = outputFolderPath;
            }

            UpdateConvertButtonState();
        }

        private void UpdateConvertButtonState()
        {
            var csvFiles = string.IsNullOrEmpty(selectedFolderPath) ? Array.Empty<string>() :
                          Directory.GetFiles(selectedFolderPath, "*.csv", SearchOption.TopDirectoryOnly);

            bool hasOutputPath = chkSameFolder.Checked || !string.IsNullOrEmpty(outputFolderPath);
            btnConvert.Enabled = csvFiles.Length > 0 && hasOutputPath;
        }

        private string GetActualOutputPath()
        {
            if (chkSameFolder.Checked)
            {
                return selectedFolderPath;
            }
            return outputFolderPath;
        }
    }
}
