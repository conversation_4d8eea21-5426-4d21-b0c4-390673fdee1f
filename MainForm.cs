using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace CsvToMarkdown
{
    public partial class MainForm : Form
    {
        private string selectedFolderPath = string.Empty;

        public MainForm()
        {
            InitializeComponent();
        }

        private void btnSelectFolder_Click(object sender, EventArgs e)
        {
            using (var folderDialog = new FolderBrowserDialog())
            {
                folderDialog.Description = "选择包含CSV文件的文件夹";
                folderDialog.ShowNewFolderButton = false;

                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    selectedFolderPath = folderDialog.SelectedPath;
                    txtFolderPath.Text = selectedFolderPath;
                    
                    // 检查文件夹中是否有CSV文件
                    var csvFiles = Directory.GetFiles(selectedFolderPath, "*.csv", SearchOption.TopDirectoryOnly);
                    lblFileCount.Text = $"找到 {csvFiles.Length} 个CSV文件";
                    btnConvert.Enabled = csvFiles.Length > 0;
                }
            }
        }

        private async void btnConvert_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(selectedFolderPath))
            {
                MessageBox.Show("请先选择文件夹", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                btnConvert.Enabled = false;
                btnSelectFolder.Enabled = false;
                progressBar.Visible = true;
                lblStatus.Text = "正在转换...";

                var converter = new CsvToMarkdownConverter();
                await converter.ConvertFolderAsync(selectedFolderPath, UpdateProgress);

                lblStatus.Text = "转换完成！";
                MessageBox.Show("所有CSV文件已成功转换为Markdown格式！", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                lblStatus.Text = "转换失败";
                MessageBox.Show($"转换过程中发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnConvert.Enabled = true;
                btnSelectFolder.Enabled = true;
                progressBar.Visible = false;
            }
        }

        private void UpdateProgress(int current, int total, string fileName)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<int, int, string>(UpdateProgress), current, total, fileName);
                return;
            }

            progressBar.Maximum = total;
            progressBar.Value = current;
            lblStatus.Text = $"正在处理: {fileName} ({current}/{total})";
        }
    }
}
