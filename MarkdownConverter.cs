using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CsvToMarkdown
{
    public class MarkdownConverter
    {
        /// <summary>
        /// 将CSV数据转换为Markdown表格格式
        /// </summary>
        /// <param name="csvData">CSV数据，第一行为表头</param>
        /// <returns>Markdown格式的表格字符串</returns>
        public static string ConvertToMarkdown(List<List<string>> csvData)
        {
            if (csvData == null || csvData.Count == 0)
            {
                return string.Empty;
            }

            var markdown = new StringBuilder();
            
            // 如果只有一行数据，将其作为表头处理
            if (csvData.Count == 1)
            {
                var headers = csvData[0];
                markdown.AppendLine(CreateMarkdownRow(headers));
                markdown.AppendLine(CreateSeparatorRow(headers.Count));
                return markdown.ToString();
            }

            // 处理表头
            var headerRow = csvData[0];
            markdown.AppendLine(CreateMarkdownRow(headerRow));
            
            // 创建分隔行
            markdown.AppendLine(CreateSeparatorRow(headerRow.Count));
            
            // 处理数据行
            for (int i = 1; i < csvData.Count; i++)
            {
                var dataRow = csvData[i];
                // 确保数据行的列数与表头一致
                var normalizedRow = NormalizeRowLength(dataRow, headerRow.Count);
                markdown.AppendLine(CreateMarkdownRow(normalizedRow));
            }
            
            return markdown.ToString();
        }
        
        /// <summary>
        /// 创建Markdown表格行
        /// </summary>
        /// <param name="cells">单元格数据</param>
        /// <returns>Markdown格式的行</returns>
        private static string CreateMarkdownRow(List<string> cells)
        {
            // 转义Markdown特殊字符并处理换行符
            var escapedCells = cells.Select(cell => EscapeMarkdownCharacters(cell ?? string.Empty));
            return "| " + string.Join(" | ", escapedCells) + " |";
        }
        
        /// <summary>
        /// 创建Markdown表格分隔行
        /// </summary>
        /// <param name="columnCount">列数</param>
        /// <returns>分隔行字符串</returns>
        private static string CreateSeparatorRow(int columnCount)
        {
            var separators = Enumerable.Repeat("---", columnCount);
            return "| " + string.Join(" | ", separators) + " |";
        }
        
        /// <summary>
        /// 标准化行长度，确保与表头列数一致
        /// </summary>
        /// <param name="row">数据行</param>
        /// <param name="targetLength">目标长度</param>
        /// <returns>标准化后的行</returns>
        private static List<string> NormalizeRowLength(List<string> row, int targetLength)
        {
            var normalizedRow = new List<string>(row);
            
            // 如果行太短，用空字符串填充
            while (normalizedRow.Count < targetLength)
            {
                normalizedRow.Add(string.Empty);
            }
            
            // 如果行太长，截断到目标长度
            if (normalizedRow.Count > targetLength)
            {
                normalizedRow = normalizedRow.Take(targetLength).ToList();
            }
            
            return normalizedRow;
        }
        
        /// <summary>
        /// 转义Markdown特殊字符
        /// </summary>
        /// <param name="text">原始文本</param>
        /// <returns>转义后的文本</returns>
        private static string EscapeMarkdownCharacters(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return string.Empty;
            }
            
            // 替换换行符为空格，避免破坏表格结构
            text = text.Replace("\r\n", " ").Replace("\r", " ").Replace("\n", " ");
            
            // 转义管道符，这是Markdown表格的关键字符
            text = text.Replace("|", "\\|");
            
            // 转义其他可能影响表格的字符
            text = text.Replace("\\", "\\\\");
            
            // 移除多余的空格
            while (text.Contains("  "))
            {
                text = text.Replace("  ", " ");
            }
            
            return text.Trim();
        }
    }
}
